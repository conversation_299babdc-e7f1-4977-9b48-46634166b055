package com.wormhole.hotelds.api.hotel.web.controller.toC;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.api.hotel.resp.BlankResp;
import com.wormhole.hotelds.api.hotel.req.TicketAdminPageReq;
import com.wormhole.hotelds.api.hotel.req.TicketInfoReq;
import com.wormhole.hotelds.api.hotel.req.TicketStatisticsReq;
import com.wormhole.hotelds.api.hotel.resp.TicketAdminListResp;
import com.wormhole.hotelds.api.hotel.resp.TicketDetailResp;
import com.wormhole.hotelds.api.hotel.resp.TicketStatisticsResp;
import com.wormhole.hotelds.api.hotel.web.service.TicketAdminService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/19
 */
@RestController
@RequiredArgsConstructor

@RequestMapping("/ticket_admin")
public class TicketAdminController {

    @Autowired
    private TicketAdminService ticketAdminService;


    @GetMapping("/service/categories")
    public Mono<Result<List<BlankResp>>> getServiceTicketCategories(){
        return ticketAdminService.getServiceTicketCategories().flatMap(Result::success);
    }

    @PostMapping("/page")
    public Mono<Result<PageResult<TicketAdminListResp>>> getTicketPage(@RequestBody TicketAdminPageReq req){
        return ticketAdminService.getTicketPage(req).flatMap(Result::success);
    }

    @PostMapping("/page_feign")
    public Mono<PageResult<TicketAdminListResp>> getTicketPageFeign(@RequestBody TicketAdminPageReq req){
        return ticketAdminService.getTicketPage(req);
    }

    @PostMapping("/detail")
    public Mono<Result<TicketDetailResp>> getTicketDetail(@RequestBody TicketInfoReq req){
        return ticketAdminService.getTicketDetail(req).flatMap(Result::success);
    }

    @PostMapping("/detail_feign")
    public Mono<TicketDetailResp> getTicketDetailFeign(@RequestBody TicketInfoReq req){
        return ticketAdminService.getTicketDetail(req);
    }

    @PostMapping("/statistics")
    public Mono<Result<List<TicketStatisticsResp>>> getTicketStatistics(@RequestBody TicketStatisticsReq req){
        return ticketAdminService.getTicketStatistics(req).flatMap(Result::success);
    }
}
